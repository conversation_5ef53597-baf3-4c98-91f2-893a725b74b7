'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { CreditCard, X } from 'lucide-react'

interface PaymentSuccessModalProps {
  isOpen: boolean
  onClose: () => void
  creditsAdded: number
  packageName: string
  transactionType: 'subscription_create' | 'subscription_upgrade' | 'subscription_downgrade' | 'credit_purchase'
}

export function PaymentSuccessModal({ 
  isOpen, 
  onClose, 
  creditsAdded, 
  packageName,
  transactionType 
}: PaymentSuccessModalProps) {
  if (!isOpen) return null

  const getTitle = () => {
    switch (transactionType) {
      case 'subscription_create':
        return 'Subscription Activated! 🎉'
      case 'subscription_renewal':
        return 'Subscription Renewed! 🔄'
      case 'subscription_upgrade':
        return 'Subscription Upgraded! ⬆️'
      case 'subscription_downgrade':
        return 'Subscription Updated! ⬇️'
      case 'credit_purchase':
        return 'Credits Purchased! 💳'
      default:
        return 'Payment Successful! ✅'
    }
  }

  const getMessage = () => {
    switch (transactionType) {
      case 'subscription_create':
        return `Thank you for choosing ${packageName}! Your subscription is now active and ${creditsAdded} credits have been added to your account. You're all set to continue analyzing documents with enhanced features.`
      case 'subscription_renewal':
        return `Your ${packageName} subscription has been renewed successfully! ${creditsAdded} credits have been added to your account for another month of powerful document analysis.`
      case 'subscription_upgrade':
        return `Congratulations on upgrading to ${packageName}! You now have access to enhanced features and ${creditsAdded} additional credits have been added to your account.`
      case 'subscription_downgrade':
        return `Your subscription has been updated to ${packageName}. ${creditsAdded > 0 ? `${creditsAdded} credits have been added to your account.` : 'Your account has been updated accordingly.'}`
      case 'credit_purchase':
        return `Thank you for your purchase! ${creditsAdded} credits have been added to your account and are ready to use for your document analysis needs.`
      default:
        return `Payment successful! ${creditsAdded} credits have been added to your account.`
    }
  }

  return (
    <div className="auth-modal-backdrop fixed inset-0 z-50 flex items-center justify-center">
      {/* Payment Success Modal */}
      <div className="auth-modal-content relative bg-white rounded-lg shadow-xl border border-gray-200 p-8 max-w-md mx-4">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-1 text-gray-400 hover:text-gray-600"
        >
          <X className="w-5 h-5" />
        </button>

        {/* Content */}
        <div className="text-center">
          {/* Icon */}
          <div className="mx-auto flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
            <CreditCard className="w-8 h-8 text-green-600" />
          </div>

          {/* Title */}
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {getTitle()}
          </h2>

          {/* Message */}
          <p className="text-gray-600 mb-6">
            {getMessage()}
          </p>

          {/* Credits Display */}
          {creditsAdded > 0 && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-1">
                  +{creditsAdded}
                </div>
                <div className="text-sm text-green-700">
                  Credits Added
                </div>
              </div>
            </div>
          )}

          {/* Button */}
          <div className="flex justify-center">
            <Button
              onClick={onClose}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              Continue
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
